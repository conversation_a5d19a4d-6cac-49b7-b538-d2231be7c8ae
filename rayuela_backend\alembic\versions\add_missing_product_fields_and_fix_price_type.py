"""Add missing product fields and fix price type to DECIMAL

Revision ID: add_missing_product_fields_and_fix_price_type
Revises: extend_rls_to_missing_tables
Create Date: 2025-01-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'add_missing_product_fields_and_fix_price_type'
down_revision = 'extend_rls_to_missing_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add missing fields to products table and fix price type."""
    
    # Step 1: Add missing fields that don't exist yet
    # Check if price column exists, if not add it as DECIMAL
    op.execute("""
        DO $$ BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'products' AND column_name = 'price'
            ) THEN
                ALTER TABLE products ADD COLUMN price DECIMAL(10, 2) NOT NULL DEFAULT 0.00;
            END IF;
        END $$;
    """)
    
    # Check if average_rating column exists, if not add it
    op.execute("""
        DO $$ BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'products' AND column_name = 'average_rating'
            ) THEN
                ALTER TABLE products ADD COLUMN average_rating FLOAT DEFAULT 0.0;
            END IF;
        END $$;
    """)
    
    # Check if num_ratings column exists, if not add it
    op.execute("""
        DO $$ BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'products' AND column_name = 'num_ratings'
            ) THEN
                ALTER TABLE products ADD COLUMN num_ratings INTEGER DEFAULT 0;
            END IF;
        END $$;
    """)
    
    # Check if inventory_count column exists, if not add it
    op.execute("""
        DO $$ BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'products' AND column_name = 'inventory_count'
            ) THEN
                ALTER TABLE products ADD COLUMN inventory_count INTEGER DEFAULT 0;
            END IF;
        END $$;
    """)
    
    # Step 2: If price column exists as Float, convert it to DECIMAL
    op.execute("""
        DO $$ BEGIN
            IF EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'products' 
                AND column_name = 'price' 
                AND data_type = 'double precision'
            ) THEN
                -- Convert Float to DECIMAL(10, 2)
                ALTER TABLE products ALTER COLUMN price TYPE DECIMAL(10, 2) USING price::DECIMAL(10, 2);
            END IF;
        END $$;
    """)
    
    # Step 3: Add indexes for better query performance
    op.execute("""
        DO $$ BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_indexes 
                WHERE tablename = 'products' AND indexname = 'idx_product_account_price'
            ) THEN
                CREATE INDEX idx_product_account_price ON products (account_id, price);
            END IF;
        END $$;
    """)
    
    op.execute("""
        DO $$ BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_indexes 
                WHERE tablename = 'products' AND indexname = 'idx_product_account_rating'
            ) THEN
                CREATE INDEX idx_product_account_rating ON products (account_id, average_rating);
            END IF;
        END $$;
    """)


def downgrade() -> None:
    """Revert changes to products table."""
    
    # Remove indexes
    op.execute("DROP INDEX IF EXISTS idx_product_account_rating")
    op.execute("DROP INDEX IF EXISTS idx_product_account_price")
    
    # Convert price back to Float if it was originally Float
    op.execute("""
        DO $$ BEGIN
            IF EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'products' 
                AND column_name = 'price' 
                AND data_type = 'numeric'
            ) THEN
                ALTER TABLE products ALTER COLUMN price TYPE FLOAT USING price::FLOAT;
            END IF;
        END $$;
    """)
    
    # Remove added columns (be careful - only remove if they were added by this migration)
    # Note: We don't remove columns in downgrade to prevent data loss
    # In production, you might want to keep the columns for safety
    pass
