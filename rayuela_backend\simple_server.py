#!/usr/bin/env python3
"""
Simple FastAPI server to test camelCase API contract.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from db.schemas.auth import RegisterRequest, Token

app = FastAPI(
    title="Rayuela API Test",
    version="1.0.0",
    docs_url="/docs",
    openapi_url="/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/auth/register", response_model=Token)
async def register(request: RegisterRequest):
    """Test registration endpoint that accepts camelCase and returns camelCase"""
    print(f"Received registration request: {request}")
    print(f"Account name: {request.account_name}")
    print(f"Email: {request.email}")
    
    # Return a token (in real app this would create account and generate real token)
    return Token(
        access_token="test_access_token_12345",
        token_type="bearer"
    )

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {"status": "healthy", "message": "API is running"}

@app.get("/")
async def root():
    """Root endpoint with API info"""
    return {
        "message": "Rayuela API Test Server",
        "docs": "/docs",
        "openapi": "/openapi.json",
        "features": [
            "camelCase JSON fields",
            "snake_case Python fields",
            "Automatic field mapping"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Rayuela API Test Server...")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔗 OpenAPI Schema: http://localhost:8000/openapi.json")
    print("🏥 Health Check: http://localhost:8000/health")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
