#!/usr/bin/env python3
"""
Test script to verify camelCase to snake_case field mapping works correctly.
"""

import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from db.schemas.auth import RegisterRequest, Token
from db.schemas.account import AccountCreate, AccountResponse

def test_register_request():
    """Test RegisterRequest with both camelCase and snake_case inputs"""
    print("Testing RegisterRequest...")
    
    # Test with camelCase (JavaScript style)
    camel_case_data = {
        "accountName": "Test Company",
        "email": "<EMAIL>", 
        "password": "password123"
    }
    
    try:
        request_camel = RegisterRequest(**camel_case_data)
        print(f"✅ camelCase input accepted: {request_camel}")
        print(f"   Field values: account_name={request_camel.account_name}")
    except Exception as e:
        print(f"❌ camelCase input failed: {e}")
    
    # Test with snake_case (Python style)
    snake_case_data = {
        "account_name": "Test Company",
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        request_snake = RegisterRequest(**snake_case_data)
        print(f"✅ snake_case input accepted: {request_snake}")
        print(f"   Field values: account_name={request_snake.account_name}")
    except Exception as e:
        print(f"❌ snake_case input failed: {e}")
    
    # Test JSON serialization (should use camelCase)
    try:
        request = RegisterRequest(**snake_case_data)
        json_output = request.model_dump(by_alias=True)
        print(f"✅ JSON serialization (camelCase): {json_output}")
        
        # Verify it contains camelCase
        if "accountName" in json_output:
            print("✅ JSON output uses camelCase as expected")
        else:
            print("❌ JSON output does not use camelCase")
            
    except Exception as e:
        print(f"❌ JSON serialization failed: {e}")

def test_token():
    """Test Token schema"""
    print("\nTesting Token...")
    
    # Test with snake_case
    token_data = {
        "access_token": "abc123",
        "token_type": "bearer"
    }
    
    try:
        token = Token(**token_data)
        json_output = token.model_dump(by_alias=True)
        print(f"✅ Token JSON output: {json_output}")
        
        # Should have camelCase in JSON
        if "accessToken" in json_output and "tokenType" in json_output:
            print("✅ Token uses camelCase in JSON output")
        else:
            print("❌ Token does not use camelCase in JSON output")
            
    except Exception as e:
        print(f"❌ Token test failed: {e}")

def test_account_create():
    """Test AccountCreate schema with multiple snake_case fields"""
    print("\nTesting AccountCreate...")
    
    # Test with mixed camelCase input
    account_data = {
        "name": "Test Account",
        "apiKeyHash": "hash123",
        "apiKeyPrefix": "sk_test",
        "apiKeyLastChars": "1234",
        "mercadopagoCustomerId": "mp_123",
        "isActive": True
    }
    
    try:
        account = AccountCreate(**account_data)
        json_output = account.model_dump(by_alias=True)
        print(f"✅ AccountCreate JSON output: {json_output}")
        
        # Check for camelCase fields
        expected_camel_fields = ["apiKeyHash", "apiKeyPrefix", "apiKeyLastChars", "mercadopagoCustomerId", "isActive"]
        found_camel_fields = [field for field in expected_camel_fields if field in json_output]
        
        if len(found_camel_fields) == len(expected_camel_fields):
            print("✅ AccountCreate uses camelCase for all fields")
        else:
            print(f"❌ AccountCreate missing camelCase fields: {set(expected_camel_fields) - set(found_camel_fields)}")
            
    except Exception as e:
        print(f"❌ AccountCreate test failed: {e}")

if __name__ == "__main__":
    print("🧪 Testing camelCase to snake_case field mapping")
    print("=" * 50)
    
    test_register_request()
    test_token()
    test_account_create()
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")
