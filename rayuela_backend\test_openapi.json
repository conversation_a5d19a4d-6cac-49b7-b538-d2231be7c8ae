{"openapi": "3.1.0", "info": {"title": "Test API", "version": "1.0.0"}, "paths": {"/auth/register": {"post": {"summary": "Register", "description": "Test registration endpoint", "operationId": "register_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"summary": "Health", "description": "Health check endpoint", "operationId": "health_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "RegisterRequest": {"properties": {"accountName": {"type": "string", "minLength": 1, "title": "Accountname", "description": "Nombre de la cuenta"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "Email del usuario administrador"}, "password": {"type": "string", "minLength": 8, "title": "Password", "description": "Contraseña del usuario administrador"}}, "type": "object", "required": ["accountName", "email", "password"], "title": "RegisterRequest", "description": "Schema para el registro completo de una cuenta con usuario administrador."}, "Token": {"properties": {"accessToken": {"type": "string", "title": "Accesstoken"}, "tokenType": {"type": "string", "title": "Tokentype"}}, "type": "object", "required": ["accessToken", "tokenType"], "title": "Token"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}